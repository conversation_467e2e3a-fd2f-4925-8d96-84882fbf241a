# 🧠 Friday AI Assistant - Web Application

This is the web frontend for your Friday AI Assistant that provides camera/video functionality with LiveKit integration.

## 🎯 What This Solves

The **LiveKit Agents Playground** you were using is just a testing interface - it's not your actual app. This web application provides:

- ✅ **Camera Access**: Uses `navigator.mediaDevices.getUserMedia()` to access your camera
- ✅ **Video Track Integration**: Creates LiveKit video tracks and sends them to your agent
- ✅ **Real-time Communication**: Connects directly to your Friday agent running on LiveKit
- ✅ **Speech Recognition**: Voice commands and responses
- ✅ **Text-to-Speech**: Friday speaks back to you
- ✅ **Modern UI**: Polished interface with Iron Man-inspired design

## 🚀 Quick Start

### 1. Start the Web Server

```bash
python web_server.py
```

This will:
- Start a local web server on `http://localhost:8080`
- Automatically open your browser
- Serve the Friday web application

### 2. Start Your Friday Agent

In a separate terminal, run your LiveKit agent:

```bash
python agent.py
```

### 3. Use the Web App

1. **Allow Permissions**: Grant camera and microphone access when prompted
2. **Start Camera**: Click "Start Camera" to begin video capture
3. **Connect to Friday**: Click "Connect to Friday" to establish connection
4. **Start Chatting**: Speak to Friday or type messages

## 📁 Files Overview

- **`index.html`**: Main web application interface
- **`app.js`**: JavaScript application logic and LiveKit integration
- **`config.js`**: LiveKit configuration and credentials
- **`web_server.py`**: Simple Python web server to serve the app

## 🔧 Configuration

### LiveKit Credentials

Update `config.js` with your LiveKit credentials:

```javascript
const LIVEKIT_CONFIG = {
    wsURL: 'wss://your-livekit-url.livekit.cloud',
    apiKey: 'your-api-key',
    apiSecret: 'your-api-secret'
};
```

Your current credentials (from memories):
- API Key: `APIPMp23qkjXV6G`
- API Secret: `VEOQYE1v2Zw8CRu9YeebYNQqLpLnfRRe4o363yUbebID`

### Environment Variables

Make sure your `.env` file contains:

```env
LIVEKIT_URL=wss://friday-jarvis-hqvtaoqombvxppjivwhk.livekit.cloud
LIVEKIT_API_KEY=APIPMp23qkjXV6G
LIVEKIT_API_SECRET=VEOQYE1v2Zw8CRu9YeebYNQqLpLnfRRe4o363yUbebID
```

## 🎮 Features

### Camera & Video
- **HD Video**: 1280x720 resolution at 30fps
- **Auto-focus**: Optimized for face detection
- **Real-time Streaming**: Low-latency video transmission to Friday

### Audio & Speech
- **Speech Recognition**: Continuous voice command detection
- **Text-to-Speech**: Friday responds with voice (pitch: 1.2, rate: 1)
- **Noise Cancellation**: Enhanced audio quality
- **Echo Cancellation**: Clear two-way communication

### AI Integration
- **Vision Capabilities**: Friday can see and analyze your video feed
- **Tool Access**: Weather, web search, email functionality
- **Real-time Responses**: Instant AI-powered assistance

## 🔍 Troubleshooting

### Camera Not Working
1. **Check Permissions**: Ensure browser has camera/microphone access
2. **HTTPS Required**: Some browsers require HTTPS for camera access
3. **Browser Compatibility**: Use Chrome, Firefox, or Safari

### Connection Issues
1. **Agent Running**: Make sure `python agent.py` is running
2. **Credentials**: Verify LiveKit credentials in `config.js`
3. **Network**: Check internet connection and firewall settings

### Audio Problems
1. **Microphone Access**: Grant microphone permissions
2. **Browser Support**: Ensure Speech Recognition API is supported
3. **Volume Levels**: Check system and browser audio settings

## 🛠️ Development

### Running on Different Port

```bash
python web_server.py 8081
```

### Adding Features

- **Custom Tools**: Add new functions to `tools.py`
- **UI Enhancements**: Modify `index.html` and CSS
- **Advanced Features**: Extend `app.js` with new capabilities

## 📱 Mobile Support

The web app is responsive and works on mobile devices:
- **Touch Controls**: Optimized for touch interfaces
- **Mobile Camera**: Access front/rear cameras
- **Responsive Design**: Adapts to different screen sizes

## 🔐 Security Notes

- **Token Generation**: Implement proper backend token generation for production
- **HTTPS**: Use HTTPS in production for security
- **API Keys**: Never expose API secrets in client-side code

## 🎯 Next Steps

1. **Test the Web App**: Start the server and test camera functionality
2. **Customize UI**: Modify the design to match your preferences
3. **Add Features**: Implement additional AI capabilities
4. **Deploy**: Set up proper hosting for production use

---

**Note**: This web application replaces the need for the LiveKit Agents Playground. You now have your own custom Friday interface with full camera and AI integration!
