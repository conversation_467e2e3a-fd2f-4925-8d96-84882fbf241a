<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Friday - Your Personal AI Assistant</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            text-align: center;
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 2.5rem;
            color: #00d4ff;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            margin-bottom: 10px;
        }

        .header p {
            color: #a0a0a0;
            font-size: 1.1rem;
        }

        .main-container {
            display: flex;
            flex: 1;
            gap: 20px;
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
            width: 100%;
        }

        .video-section {
            flex: 1;
            background: rgba(0, 0, 0, 0.4);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 212, 255, 0.2);
        }

        .video-container {
            position: relative;
            width: 100%;
            height: 400px;
            background: #000;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
        }

        #localVideo {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .video-placeholder {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            background: linear-gradient(45deg, #1a1a2e, #16213e);
            color: #666;
            font-size: 1.2rem;
        }

        .controls {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 212, 255, 0.4);
        }

        .btn-danger {
            background: linear-gradient(45deg, #ff4757, #ff3742);
            color: white;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 71, 87, 0.4);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .chat-section {
            flex: 1;
            background: rgba(0, 0, 0, 0.4);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 212, 255, 0.2);
            display: flex;
            flex-direction: column;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            min-height: 300px;
        }

        .message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 10px;
            max-width: 80%;
        }

        .message.user {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            margin-left: auto;
            text-align: right;
        }

        .message.assistant {
            background: rgba(255, 255, 255, 0.1);
            border-left: 3px solid #00d4ff;
        }

        .status {
            text-align: center;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .status.connected {
            border-left: 4px solid #00ff88;
            color: #00ff88;
        }

        .status.disconnected {
            border-left: 4px solid #ff4757;
            color: #ff4757;
        }

        .status.connecting {
            border-left: 4px solid #ffa502;
            color: #ffa502;
        }

        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 Friday</h1>
        <p>Your Personal AI Assistant with Vision</p>
    </div>

    <div class="main-container">
        <div class="video-section">
            <h3 style="margin-bottom: 15px; color: #00d4ff;">📷 Camera Feed</h3>
            <div class="video-container">
                <video id="localVideo" autoplay muted playsinline style="display: none;"></video>
                <div id="videoPlaceholder" class="video-placeholder">
                    Camera not started
                </div>
            </div>
            <div class="controls">
                <button id="startCamera" class="btn btn-primary">Start Camera</button>
                <button id="stopCamera" class="btn btn-danger" disabled>Stop Camera</button>
                <button id="connectAgent" class="btn btn-primary" disabled>Connect to Friday</button>
                <button id="disconnectAgent" class="btn btn-danger" disabled>Disconnect</button>
            </div>
        </div>

        <div class="chat-section">
            <h3 style="margin-bottom: 15px; color: #00d4ff;">💬 Chat with Friday</h3>
            <div id="status" class="status disconnected">
                Disconnected - Start camera and connect to begin
            </div>
            <div id="chatMessages" class="chat-messages">
                <div class="message assistant">
                    <strong>Friday:</strong> Hello! I'm Friday, your personal AI assistant. Start your camera and connect to begin our conversation.
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/livekit-client/dist/livekit-client.umd.js"></script>
    <script src="config.js"></script>
    <script src="app.js"></script>
</body>
</html>
