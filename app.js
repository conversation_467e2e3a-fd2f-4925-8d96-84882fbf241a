class FridayApp {
    constructor() {
        this.room = null;
        this.localVideoTrack = null;
        this.localAudioTrack = null;
        this.isConnected = false;
        this.isRecording = false;
        
        // LiveKit configuration - you'll need to update these with your actual values
        this.wsURL = 'wss://friday-jarvis-hqvtaoqombvxppjivwhk.livekit.cloud';
        this.token = null; // Will be generated or provided
        
        this.initializeElements();
        this.setupEventListeners();
    }

    initializeElements() {
        this.localVideo = document.getElementById('localVideo');
        this.videoPlaceholder = document.getElementById('videoPlaceholder');
        this.chatMessages = document.getElementById('chatMessages');
        this.status = document.getElementById('status');
        
        this.startCameraBtn = document.getElementById('startCamera');
        this.stopCameraBtn = document.getElementById('stopCamera');
        this.connectAgentBtn = document.getElementById('connectAgent');
        this.disconnectAgentBtn = document.getElementById('disconnectAgent');
    }

    setupEventListeners() {
        this.startCameraBtn.addEventListener('click', () => this.startCamera());
        this.stopCameraBtn.addEventListener('click', () => this.stopCamera());
        this.connectAgentBtn.addEventListener('click', () => this.connectToAgent());
        this.disconnectAgentBtn.addEventListener('click', () => this.disconnectFromAgent());
    }

    async startCamera() {
        try {
            this.updateStatus('Requesting camera access...', 'connecting');
            
            // Request camera and microphone access
            const stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    width: { ideal: 1280 },
                    height: { ideal: 720 },
                    facingMode: 'user'
                },
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                }
            });

            // Create LiveKit tracks
            this.localVideoTrack = new LiveKitClient.LocalVideoTrack(stream.getVideoTracks()[0]);
            this.localAudioTrack = new LiveKitClient.LocalAudioTrack(stream.getAudioTracks()[0]);
            
            // Store video track globally for LiveKit integration (as mentioned in memories)
            window.videoTrack = this.localVideoTrack;

            // Display video
            this.localVideo.srcObject = stream;
            this.localVideo.style.display = 'block';
            this.videoPlaceholder.style.display = 'none';

            // Update UI
            this.startCameraBtn.disabled = true;
            this.stopCameraBtn.disabled = false;
            this.connectAgentBtn.disabled = false;
            
            this.updateStatus('Camera started - Ready to connect', 'connecting');
            this.addMessage('System', 'Camera access granted. You can now connect to Friday.', 'assistant');

        } catch (error) {
            console.error('Error starting camera:', error);
            this.updateStatus('Camera access denied', 'disconnected');
            this.addMessage('System', 'Camera access was denied. Please allow camera access and try again.', 'assistant');
        }
    }

    async stopCamera() {
        if (this.localVideoTrack) {
            this.localVideoTrack.stop();
            this.localVideoTrack = null;
        }
        
        if (this.localAudioTrack) {
            this.localAudioTrack.stop();
            this.localAudioTrack = null;
        }

        this.localVideo.style.display = 'none';
        this.videoPlaceholder.style.display = 'flex';
        
        this.startCameraBtn.disabled = false;
        this.stopCameraBtn.disabled = true;
        this.connectAgentBtn.disabled = true;
        
        if (this.isConnected) {
            await this.disconnectFromAgent();
        }
        
        this.updateStatus('Camera stopped', 'disconnected');
        this.addMessage('System', 'Camera has been stopped.', 'assistant');
    }

    async connectToAgent() {
        try {
            this.updateStatus('Connecting to Friday...', 'connecting');
            
            // Generate a simple token for demo purposes
            // In production, you should get this from your backend
            const roomName = 'friday-session-' + Date.now();
            const participantName = 'User';
            
            // For demo purposes, we'll use a simple room connection
            // You'll need to implement proper token generation on your backend
            this.room = new LiveKitClient.Room({
                adaptiveStream: true,
                dynacast: true,
            });

            // Set up event listeners
            this.setupRoomEventListeners();

            // Connect to room (you'll need to implement token generation)
            await this.connectToRoom(roomName, participantName);
            
        } catch (error) {
            console.error('Error connecting to agent:', error);
            this.updateStatus('Connection failed', 'disconnected');
            this.addMessage('System', 'Failed to connect to Friday. Please check your connection and try again.', 'assistant');
        }
    }

    async connectToRoom(roomName, participantName) {
        // This is a simplified connection - you'll need to implement proper token generation
        // For now, we'll simulate the connection
        
        try {
            // Simulate connection delay
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            this.isConnected = true;
            this.updateStatus('Connected to Friday', 'connected');
            this.connectAgentBtn.disabled = true;
            this.disconnectAgentBtn.disabled = false;
            
            this.addMessage('Friday', 'Hi my name is Friday, your personal assistant, how may I help you?', 'assistant');
            
            // Start speech recognition for voice commands
            this.startSpeechRecognition();
            
        } catch (error) {
            throw new Error('Failed to connect to LiveKit room');
        }
    }

    setupRoomEventListeners() {
        if (!this.room) return;

        this.room.on('participantConnected', (participant) => {
            console.log('Participant connected:', participant.identity);
        });

        this.room.on('trackSubscribed', (track, publication, participant) => {
            if (track.kind === 'audio' && participant.identity === 'Friday') {
                // Handle Friday's audio response
                const audioElement = track.attach();
                document.body.appendChild(audioElement);
            }
        });

        this.room.on('disconnected', () => {
            this.handleDisconnection();
        });
    }

    async disconnectFromAgent() {
        if (this.room) {
            await this.room.disconnect();
            this.room = null;
        }
        
        this.handleDisconnection();
    }

    handleDisconnection() {
        this.isConnected = false;
        this.updateStatus('Disconnected from Friday', 'disconnected');
        this.connectAgentBtn.disabled = this.localVideoTrack ? false : true;
        this.disconnectAgentBtn.disabled = true;
        
        this.addMessage('System', 'Disconnected from Friday.', 'assistant');
        
        if (this.recognition) {
            this.recognition.stop();
        }
    }

    startSpeechRecognition() {
        if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
            console.log('Speech recognition not supported');
            return;
        }

        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        this.recognition = new SpeechRecognition();
        
        this.recognition.continuous = true;
        this.recognition.interimResults = true;
        this.recognition.lang = 'en-US';

        this.recognition.onresult = (event) => {
            let finalTranscript = '';
            
            for (let i = event.resultIndex; i < event.results.length; i++) {
                if (event.results[i].isFinal) {
                    finalTranscript += event.results[i][0].transcript;
                }
            }
            
            if (finalTranscript.trim()) {
                this.addMessage('You', finalTranscript, 'user');
                this.sendMessageToFriday(finalTranscript);
            }
        };

        this.recognition.onerror = (event) => {
            console.error('Speech recognition error:', event.error);
        };

        this.recognition.start();
    }

    async sendMessageToFriday(message) {
        // This would normally send the message to your LiveKit agent
        // For demo purposes, we'll simulate a response
        
        setTimeout(() => {
            const responses = [
                "Roger that, sir. I'll take care of that right away.",
                "Of course, as you wish. Consider it done.",
                "Will do, sir. Processing your request now.",
                "Check! I'm on it immediately.",
                "Understood, boss. Handling that for you."
            ];
            
            const randomResponse = responses[Math.floor(Math.random() * responses.length)];
            this.addMessage('Friday', randomResponse, 'assistant');
            
            // Use text-to-speech as mentioned in memories
            this.speakText(randomResponse);
        }, 1000);
    }

    speakText(text) {
        if ('speechSynthesis' in window) {
            const utterance = new SpeechSynthesisUtterance(text);
            utterance.lang = 'en-US';
            utterance.pitch = 1.2;
            utterance.rate = 1;
            speechSynthesis.speak(utterance);
        }
    }

    addMessage(sender, message, type) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        messageDiv.innerHTML = `<strong>${sender}:</strong> ${message}`;
        
        this.chatMessages.appendChild(messageDiv);
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }

    updateStatus(message, type) {
        this.status.textContent = message;
        this.status.className = `status ${type}`;
    }
}

// Initialize the app when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.fridayApp = new FridayApp();
});
