// LiveKit Configuration for Friday AI Assistant
// Update these values with your actual LiveKit credentials

const LIVEKIT_CONFIG = {
    // Your LiveKit WebSocket URL
    // From your memories: you have new LiveKit API credentials
    wsURL: 'wss://friday-jarvis-hqvtaoqombvxppjivwhk.livekit.cloud',
    
    // API Key and Secret (from your memories)
    apiKey: 'APIPMp23qkjXV6G',
    apiSecret: 'VEOQYE1v2Zw8CRu9YeebYNQqLpLnfRRe4o363yUbebID',
    
    // Room configuration
    roomConfig: {
        adaptiveStream: true,
        dynacast: true,
        videoCaptureDefaults: {
            resolution: {
                width: 1280,
                height: 720,
                frameRate: 30
            }
        },
        audioCaptureDefaults: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
        }
    },
    
    // Agent configuration
    agentConfig: {
        name: 'Friday',
        identity: 'friday-assistant',
        metadata: {
            type: 'ai-assistant',
            capabilities: ['vision', 'speech', 'tools']
        }
    }
};

// Token generation function (simplified for demo)
// In production, this should be done on your backend server
function generateAccessToken(roomName, participantName) {
    // This is a simplified token generation for demo purposes
    // You should implement proper JWT token generation on your backend
    
    const payload = {
        iss: LIVEKIT_CONFIG.apiKey,
        sub: participantName,
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours
        room: roomName,
        grants: {
            room: roomName,
            roomJoin: true,
            canPublish: true,
            canSubscribe: true,
            canPublishData: true
        }
    };
    
    // Note: This is a placeholder. In a real application, you would:
    // 1. Send a request to your backend server
    // 2. Your backend would generate a proper JWT token using the LiveKit SDK
    // 3. Return the token to the frontend
    
    console.warn('Using simplified token generation. Implement proper backend token generation for production.');
    
    // For demo purposes, return a mock token structure
    return btoa(JSON.stringify(payload));
}

// Export configuration for use in app.js
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { LIVEKIT_CONFIG, generateAccessToken };
} else {
    window.LIVEKIT_CONFIG = LIVEKIT_CONFIG;
    window.generateAccessToken = generateAccessToken;
}
