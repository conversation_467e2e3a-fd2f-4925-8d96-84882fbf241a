#!/usr/bin/env python3
"""
Generate LiveKit room token for connecting to the playground.
"""

import os
import time
from livekit import api
from dotenv import load_dotenv

load_dotenv()

def generate_room_token(room_name="friday-playground", participant_name="User"):
    """Generate a LiveKit room token for playground access."""
    
    # Get credentials from environment
    api_key = os.getenv("LIVEKIT_API_KEY")
    api_secret = os.getenv("LIVEKIT_API_SECRET")
    livekit_url = os.getenv("LIVEKIT_URL")
    
    if not all([api_key, api_secret, livekit_url]):
        print("❌ Missing LiveKit credentials in .env file")
        return None
    
    # Create token with permissions
    token = api.AccessToken(api_key, api_secret) \
        .with_identity(participant_name) \
        .with_name(participant_name) \
        .with_grants(api.VideoGrants(
            room_join=True,
            room=room_name,
            can_publish=True,
            can_subscribe=True,
            can_publish_data=True
        ))
    
    # Set expiration (24 hours from now)
    import datetime
    token.ttl = datetime.timedelta(hours=24)
    
    return token.to_jwt()

def main():
    print("🧠 Friday AI Assistant - LiveKit Token Generator")
    print("=" * 60)
    
    # Get room name from user or use default
    room_name = input("Enter room name (or press Enter for 'friday-playground'): ").strip()
    if not room_name:
        room_name = "friday-playground"
    
    # Get participant name from user or use default
    participant_name = input("Enter your name (or press Enter for 'User'): ").strip()
    if not participant_name:
        participant_name = "User"
    
    print(f"\n🔑 Generating token for:")
    print(f"   Room: {room_name}")
    print(f"   Participant: {participant_name}")
    print()
    
    # Generate token
    token = generate_room_token(room_name, participant_name)
    
    if token:
        print("✅ Token generated successfully!")
        print("=" * 60)
        print("📋 COPY THESE DETAILS TO LIVEKIT PLAYGROUND:")
        print("=" * 60)
        print(f"WebSocket URL: {os.getenv('LIVEKIT_URL')}")
        print(f"Room Token: {token}")
        print("=" * 60)
        print()
        print("🎯 Instructions:")
        print("1. Go to LiveKit Agents Playground")
        print("2. Select 'Manual' connection")
        print("3. Paste the WebSocket URL above")
        print("4. Paste the Room Token above")
        print("5. Click 'Connect'")
        print()
        print("🤖 Make sure your Friday agent is running:")
        print("   python agent.py dev")
        print()
    else:
        print("❌ Failed to generate token")

if __name__ == "__main__":
    main()
