#!/usr/bin/env python3
"""
Simple web server to serve the Friday AI Assistant web application.
This serves the HTML/JS frontend that connects to the LiveKit agent.
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

class FridayWebHandler(http.server.SimpleHTTPRequestHandler):
    """Custom handler to serve the Friday web app with proper MIME types."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
    
    def end_headers(self):
        # Add CORS headers for development
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_GET(self):
        # Serve index.html for root path
        if self.path == '/':
            self.path = '/index.html'
        return super().do_GET()

def start_server(port=8080):
    """Start the web server for the Friday AI Assistant."""
    
    # Check if required files exist
    required_files = ['index.html', 'app.js']
    missing_files = []
    
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing required files: {', '.join(missing_files)}")
        print("Please make sure all web app files are in the current directory.")
        return False
    
    try:
        with socketserver.TCPServer(("", port), FridayWebHandler) as httpd:
            print("🧠 Friday AI Assistant Web Server")
            print("=" * 50)
            print(f"🌐 Server running at: http://localhost:{port}")
            print(f"📁 Serving files from: {os.getcwd()}")
            print("=" * 50)
            print("📋 Instructions:")
            print("1. Open the URL above in your web browser")
            print("2. Allow camera and microphone access when prompted")
            print("3. Click 'Start Camera' to begin")
            print("4. Click 'Connect to Friday' to start chatting")
            print("=" * 50)
            print("Press Ctrl+C to stop the server")
            print()
            
            # Try to open browser automatically
            try:
                webbrowser.open(f'http://localhost:{port}')
                print("🚀 Opening browser automatically...")
            except Exception as e:
                print(f"⚠️  Could not open browser automatically: {e}")
                print(f"Please manually open: http://localhost:{port}")
            
            print()
            httpd.serve_forever()
            
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ Port {port} is already in use.")
            print(f"Try a different port: python web_server.py {port + 1}")
        else:
            print(f"❌ Error starting server: {e}")
        return False
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
        return True

def main():
    """Main function to handle command line arguments and start server."""
    
    # Default port
    port = 8080
    
    # Check for custom port in command line arguments
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
            if port < 1024 or port > 65535:
                print("❌ Port must be between 1024 and 65535")
                return
        except ValueError:
            print("❌ Invalid port number. Please provide a valid integer.")
            return
    
    # Start the server
    start_server(port)

if __name__ == "__main__":
    main()
